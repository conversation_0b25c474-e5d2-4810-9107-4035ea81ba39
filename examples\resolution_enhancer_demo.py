import cv2
import numpy as np
import time
from camera_optimizer import ResolutionEnhancer

def main():
    print("=== Demonstração de Melhoria de Resolução da LifeCam VX-2000 ===")
    print("Pressione:")
    print("  '1' - Resolução Original (640x480)")
    print("  '2' - Resolução 720p (1280x720)")
    print("  '3' - Resolução 1080p (1920x1080)")
    print("  '4' - Resolução 480p (854x480)")
    print("  'q' - Sair")
    
    # Inicializa a câmera
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("ERRO: Não foi possível abrir a câmera!")
        return
        
    # Configura a câmera para resolução original
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    # Inicializa o enhancer com resolução padrão 720p
    enhancer = ResolutionEnhancer(target_resolution="720p", use_gpu=True)
    current_resolution = "720p"
    
    # Cria janela de visualização
    cv2.namedWindow("Resolução Melhorada", cv2.WINDOW_NORMAL)
    
    # Métricas de performance
    frame_times = []
    start_time = time.time()
    frames_processed = 0
    
    while True:
        # Captura frame
        ret, frame = cap.read()
        if not ret:
            print("ERRO: Falha ao capturar frame!")
            break
            
        # Mede tempo de processamento
        t1 = time.time()
        
        # Processa frame para melhorar resolução
        enhanced_frame = enhancer.process(frame)
        
        # Calcula tempo de processamento
        processing_time = (time.time() - t1) * 1000  # ms
        frame_times.append(processing_time)
        frames_processed += 1
        
        # Mostra informações no frame
        h, w = enhanced_frame.shape[:2]
        fps = frames_processed / (time.time() - start_time)
        avg_time = sum(frame_times[-30:]) / min(len(frame_times), 30)
        
        info_text = f"Resolução: {w}x{h} ({current_resolution}) | FPS: {fps:.1f} | Latência: {avg_time:.1f}ms"
        cv2.putText(enhanced_frame, info_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Mostra o frame
        cv2.imshow("Resolução Melhorada", enhanced_frame)
        
        # Processa teclas
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('1'):
            enhancer = ResolutionEnhancer(target_resolution="original", use_gpu=True)
            current_resolution = "original"
        elif key == ord('2'):
            enhancer = ResolutionEnhancer(target_resolution="720p", use_gpu=True)
            current_resolution = "720p"
        elif key == ord('3'):
            enhancer = ResolutionEnhancer(target_resolution="1080p", use_gpu=True)
            current_resolution = "1080p"
        elif key == ord('4'):
            enhancer = ResolutionEnhancer(target_resolution="480p", use_gpu=True)
            current_resolution = "480p"
    
    # Libera recursos
    cap.release()
    cv2.destroyAllWindows()
    
    # Mostra estatísticas finais
    if frame_times:
        print("\nEstatísticas de Performance:")
        print(f"Tempo médio de processamento: {sum(frame_times) / len(frame_times):.2f}ms")
        print(f"FPS médio: {frames_processed / (time.time() - start_time):.2f}")
        print(f"Frames processados: {frames_processed}")

if __name__ == "__main__":
    main() 