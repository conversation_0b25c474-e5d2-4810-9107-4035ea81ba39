import numpy as np
from reedsolo import RSCodec
from typing import List, Tuple
import cv2

class FrameRedundancyEncoder:
    def __init__(self, nsym: int = 10):
        """
        Inicializa o encoder com redundância.
        nsym: número de símbolos de correção de erro
        """
        self.rsc = RSCodec(nsym)
        self._frame_buffer = []

    def _split_frame(self, frame: np.ndarray, chunk_size: int = 1024) -> List[bytes]:
        """
        Divide o frame em chunks menores para codificação.
        """
        frame_bytes = frame.tobytes()
        return [frame_bytes[i:i+chunk_size] for i in range(0, len(frame_bytes), chunk_size)]

    def _encode_chunk(self, chunk: bytes) -> bytes:
        """
        Aplica codificação Reed-Solomon em um chunk.
        """
        return self.rsc.encode(chunk)

    def encode_frame(self, frame: np.ndarray, redundancy_factor: float = 1.5) -> np.ndarray:
        """
        Codifica um frame com redundância para recuperação de perdas.
        """
        # Comprime o frame para reduzir tamanho
        _, compressed = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
        frame_data = compressed.tobytes()

        # Divide em chunks e aplica Reed-Solomon
        chunks = self._split_frame(frame_data)
        encoded_chunks = [self._encode_chunk(chunk) for chunk in chunks]

        # Adiciona redundância extra baseada no fator
        extra_chunks = int(len(chunks) * (redundancy_factor - 1))
        if extra_chunks > 0:
            encoded_chunks.extend(encoded_chunks[:extra_chunks])

        # Reconstrói o frame
        encoded_data = b''.join(encoded_chunks)
        return np.frombuffer(encoded_data, dtype=np.uint8)

    def decode_frame(self, encoded_frame: np.ndarray, shape: Tuple[int, int, int]) -> np.ndarray:
        """
        Decodifica um frame com correção de erros.
        """
        try:
            # Decodifica os chunks com correção de erros
            decoded_data = self.rsc.decode(encoded_frame.tobytes())
            
            # Reconstrói o frame
            frame_array = np.frombuffer(decoded_data, dtype=np.uint8)
            return cv2.imdecode(frame_array, cv2.IMREAD_COLOR)

        except Exception as e:
            print(f"Erro na decodificação: {e}")
            # Retorna o último frame válido em caso de erro
            return self._frame_buffer[-1] if self._frame_buffer else np.zeros(shape, dtype=np.uint8)

    def update_buffer(self, frame: np.ndarray):
        """
        Atualiza o buffer de frames para recuperação.
        """
        self._frame_buffer.append(frame)
        if len(self._frame_buffer) > 5:  # Mantém apenas os últimos 5 frames
            self._frame_buffer.pop(0) 