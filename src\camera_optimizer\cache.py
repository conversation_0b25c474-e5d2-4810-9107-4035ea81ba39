import numpy as np
import cv2
from collections import deque

class PredictiveFrameCache:
    def __init__(self, buffer_size=5):
        self.frame_buffer = deque(maxlen=buffer_size)
        self.motion_buffer = deque(maxlen=buffer_size)
        
    def calculate_motion_vector(self, prev_frame, curr_frame):
        if prev_frame is None or curr_frame is None:
            return None
            
        # Converte para escala de cinza
        prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
        curr_gray = cv2.cvtColor(curr_frame, cv2.COLOR_BGR2GRAY)
        
        # Calcula fluxo óptico
        flow = cv2.calcOpticalFlowFarneback(
            prev_gray, curr_gray, None, 0.5, 3, 15, 3, 5, 1.2, 0
        )
        
        return flow
        
    def predict_next_frame(self, current_frame):
        if len(self.frame_buffer) < 2:
            self.frame_buffer.append(current_frame)
            return current_frame
            
        # <PERSON>cula vetor de movimento
        motion = self.calculate_motion_vector(
            self.frame_buffer[-1],
            current_frame
        )
        
        if motion is not None:
            self.motion_buffer.append(motion)
            
        # Atualiza buffer
        self.frame_buffer.append(current_frame)
        
        if len(self.motion_buffer) < 2:
            return current_frame
            
        # Prediz próximo frame baseado no padrão de movimento
        predicted = current_frame.copy()
        avg_motion = np.mean(list(self.motion_buffer), axis=0)
        
        h, w = current_frame.shape[:2]
        y, x = np.mgrid[0:h, 0:w].reshape(2, -1).astype(np.float32)
        
        x = x + avg_motion[..., 0].flatten()
        y = y + avg_motion[..., 1].flatten()
        
        x = np.clip(x, 0, w-1)
        y = np.clip(y, 0, h-1)
        
        coordinates = np.vstack((x, y))
        
        for c in range(3):  # para cada canal de cor
            predicted[..., c] = cv2.remap(
                current_frame[..., c],
                coordinates[0].reshape(h, w),
                coordinates[1].reshape(h, w),
                cv2.INTER_LINEAR
            )
            
        return predicted 