# Estrutura do Projeto Microsoft LifeCam VX-2000

```
microsoft_cam/
│
├── src/
│   └── camera_optimizer/
│       ├── __init__.py
│       ├── core/
│       │   ├── __init__.py
│       │   ├── camera.py
│       │   └── utils.py
│       ├── streaming/
│       │   ├── __init__.py
│       │   ├── client.py
│       │   ├── server.py
│       │   └── processor.py
│       └── enhancement/
│           ├── __init__.py
│           ├── resolution.py
│           └── quality.py
│
├── tests/
│   ├── __init__.py
│   ├── unit/
│   │   ├── __init__.py
│   │   ├── test_camera.py
│   │   └── test_processor.py
│   └── integration/
│       ├── __init__.py
│       └── test_streaming.py
│
├── examples/
│   ├── basic_usage.py
│   ├── streaming_client.py
│   └── streaming_server.py
│
├── scripts/
│   ├── run/
│   │   ├── run_client.bat
│   │   ├── run_server.bat
│   │   ├── run_client_optimized.bat
│   │   └── run_server_optimized.bat
│   └── test/
│       ├── run_tests.bat
│       └── test_camera.bat
│
├── docs/
│   ├── api/
│   │   └── streaming.md
│   ├── setup/
│   │   └── installation.md
│   └── usage/
│       └── examples.md
│
├── requirements/
│   ├── base.txt
│   ├── dev.txt
│   └── test.txt
│
├── setup.py
├── README.md
└── .gitignore
```

## Descrição dos Diretórios

### `/src/camera_optimizer/`
- Código fonte principal do projeto
- Organizado em módulos específicos (core, streaming, enhancement)

### `/tests/`
- Testes unitários e de integração
- Separados por tipo de teste

### `/examples/`
- Exemplos práticos de uso
- Scripts demonstrativos

### `/scripts/`
- Scripts de execução (.bat)
- Organizados por funcionalidade

### `/docs/`
- Documentação do projeto
- Guias de instalação e uso
- Documentação da API

### `/requirements/`
- Arquivos de dependências
- Separados por ambiente (base, dev, test) 