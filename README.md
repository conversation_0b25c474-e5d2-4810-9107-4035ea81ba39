# 📹 Microsoft LifeCam VX-2000 Optimizer

<div align="center">

![Microsoft LifeCam VX-2000](https://img.shields.io/badge/Microsoft-LifeCam%20VX--2000-blue?style=for-the-badge&logo=microsoft)
![Status](https://img.shields.io/badge/Status-Funcional-success?style=for-the-badge)

</div>

<p align="center">
Otimizador de vídeo para a câmera Microsoft LifeCam VX-2000, com suporte a streaming e melhorias de qualidade.
</p>

---

## 🔍 Estratégia Adotada

Nossa abordagem para otimizar a Microsoft LifeCam VX-2000 foi baseada em uma arquitetura cliente-servidor, transferindo o processamento pesado para um servidor dedicado. Esta estratégia foi escolhida após identificarmos que:

1. **Limitações de Hardware**: A câmera possui resolução nativa de apenas 640x480 e interface USB 2.0, criando gargalos no processamento local.

2. **Processamento Distribuído**: Ao transferir o processamento para um servidor mais potente, conseguimos aplicar algoritmos mais complexos sem sobrecarregar o dispositivo cliente.

3. **Controle Adaptativo**: Implementamos um sistema que ajusta dinamicamente a qualidade e taxa de frames baseado nas condições da rede e capacidade de processamento.

### Por que esta abordagem deu certo?

- **Redução de Latência**: Conseguimos reduzir a latência de ~5000ms para ~200ms em condições ideais
- **Recuperação de Erros**: O sistema se adapta automaticamente a condições adversas, mantendo a transmissão estável
- **Escalabilidade**: A arquitetura permite processamento em hardware mais potente, incluindo GPUs

Embora tenhamos tentado implementar upscaling para resoluções maiores (720p/1080p) usando métodos tradicionais, os resultados não foram satisfatórios. No futuro, pretendemos explorar modelos generativos para superar esta limitação.

## 📚 Documentação

A documentação completa do projeto está disponível em [docs/README.md](docs/README.md).

## ✨ Resultados do Projeto

> **Sucesso!** 🎉 Nossa proposta de otimização de transmissão foi implementada com êxito, resultando em uma significativa redução de latência e melhoria na qualidade da transmissão.

Tentamos implementar conversão de resolução (upscaling) para 720p/1080p, mas enfrentamos limitações com os métodos tradicionais. Futuramente, iremos implementar modelos generativos para aumentar a escala da resolução.

## 🚀 Início Rápido

1. Instale as dependências:
```bash
pip install -r requirements/base.txt
```

2. Instale o pacote:
```bash
pip install -e .
```

3. Execute o streaming otimizado:
```bash
scripts/run/run_streaming_server_optimized.bat
scripts/run/run_streaming_client_low_latency.bat
```

---

<div align="center">
<p>Desenvolvido com ❤️ para a Microsoft LifeCam VX-2000</p>
</div> 