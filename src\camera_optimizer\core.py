import cv2
import numpy as np
from typing import Optional, Dict, Any
from .pipeline import OptimizedPipeline
from .cache import PredictiveFrameCache
from .encoders import FrameRedundancyEncoder

class CameraOptimizer:
    def __init__(
        self,
        device_id: int = 0,
        config: Optional[Dict[str, Any]] = None
    ):
        self.device_id = device_id
        self.config = config or self._default_config()
        self.pipeline = OptimizedPipeline()
        self.cache = PredictiveFrameCache()
        self.encoder = FrameRedundancyEncoder()
        self._setup_camera()

    def _default_config(self) -> Dict[str, Any]:
        return {
            'resolution': (640, 480),
            'fps': 30,
            'redundancy_factor': 1.5,
            'use_gpu': True,
            'buffer_size': 3,
            'compression_quality': 85
        }

    def _setup_camera(self) -> None:
        self.cap = cv2.VideoCapture(self.device_id)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config['resolution'][0])
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config['resolution'][1])
        self.cap.set(cv2.CAP_PROP_FPS, self.config['fps'])
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, self.config['buffer_size'])

    def optimize_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        Aplica pipeline completo de otimização em um frame.
        """
        # Predição de próximo frame para redução de latência
        predicted_frame = self.cache.predict_next_frame(frame)
        
        # Processamento paralelo de otimizações
        processed_frame = self.pipeline.process_frame(frame)
        
        # Codificação com redundância para recuperação de perdas
        encoded_frame = self.encoder.encode_frame(
            processed_frame,
            self.config['redundancy_factor']
        )
        
        return encoded_frame

    def start_capture(self):
        """
        Inicia captura contínua com otimização em tempo real.
        """
        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    break

                optimized_frame = self.optimize_frame(frame)
                yield optimized_frame

        except Exception as e:
            print(f"Erro durante captura: {e}")
        finally:
            self.cap.release()

    def adjust_parameters(self, **kwargs):
        """
        Ajusta parâmetros de otimização em tempo real.
        """
        for key, value in kwargs.items():
            if key in self.config:
                self.config[key] = value
                if key in ['resolution', 'fps', 'buffer_size']:
                    self._setup_camera()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if hasattr(self, 'cap'):
            self.cap.release() 