import cv2
import numpy as np
import torch
import torch.nn.functional as F
from PIL import Image
from torchvision import transforms

class SmartUpscaler:
    def __init__(self, scale_factor=2.0, use_gpu=False):
        self.scale_factor = scale_factor
        self.device = torch.device('cuda' if torch.cuda.is_available() and use_gpu else 'cpu')
        self.transform = transforms.Compose([
            transforms.ToTensor(),
        ])
        
    def detect_important_areas(self, frame):
        """Detecta áreas importantes no frame (faces, texto, etc)"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Detector de bordas para encontrar áreas de detalhe
        edges = cv2.Canny(gray, 100, 200)
        
        # Detector de faces (simplificado)
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        faces = face_cascade.detectMultiScale(gray, 1.3, 5)
        
        # Cria máscara de importância
        importance_mask = np.zeros_like(gray, dtype=np.float32)
        
        # Adiciona bordas à máscara
        importance_mask[edges > 0] = 0.5
        
        # Adiciona faces à máscara
        for (x, y, w, h) in faces:
            importance_mask[y:y+h, x:x+w] = 1.0
            
        return importance_mask
        
    def upscale_region(self, region):
        """Aplica super-resolução em uma região específica"""
        # Converte para tensor
        tensor = self.transform(Image.fromarray(region)).unsqueeze(0).to(self.device)
        
        # Upscaling usando interpolação bicúbica
        upscaled = F.interpolate(
            tensor, 
            scale_factor=self.scale_factor, 
            mode='bicubic',
            align_corners=False
        )
        
        # Converte de volta para numpy
        result = upscaled.squeeze(0).permute(1, 2, 0).mul(255).clamp(0, 255).byte().cpu().numpy()
        
        return result
        
    def upscale(self, frame):
        """Aplica upscaling inteligente no frame"""
        # Detecta áreas importantes
        importance_mask = self.detect_important_areas(frame)
        
        # Upscaling básico do frame inteiro
        h, w = frame.shape[:2]
        basic_upscaled = cv2.resize(
            frame, 
            (int(w * self.scale_factor), int(h * self.scale_factor)),
            interpolation=cv2.INTER_LINEAR
        )
        
        # Para frames pequenos, aplica upscaling em todo o frame
        if w * h < 640 * 480:
            return self.upscale_region(frame)
            
        # Para frames maiores, processa apenas regiões importantes
        result = basic_upscaled.copy()
        
        # Encontra regiões de interesse
        contours, _ = cv2.findContours(
            (importance_mask > 0.7).astype(np.uint8), 
            cv2.RETR_EXTERNAL, 
            cv2.CHAIN_APPROX_SIMPLE
        )
        
        # Processa cada região importante
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            if w * h > 32 * 32:  # Ignora regiões muito pequenas
                # Expande um pouco a região
                x_exp = max(0, x - 10)
                y_exp = max(0, y - 10)
                w_exp = min(frame.shape[1] - x_exp, w + 20)
                h_exp = min(frame.shape[0] - y_exp, h + 20)
                
                # Extrai região
                region = frame[y_exp:y_exp+h_exp, x_exp:x_exp+w_exp]
                
                # Aplica upscaling de alta qualidade
                upscaled_region = self.upscale_region(region)
                
                # Insere de volta no resultado
                y_target = int(y_exp * self.scale_factor)
                x_target = int(x_exp * self.scale_factor)
                result[
                    y_target:y_target+upscaled_region.shape[0],
                    x_target:x_target+upscaled_region.shape[1]
                ] = upscaled_region
                
        return result


class PerceptualCompressor:
    def __init__(self, quality_base=85):
        self.quality_base = quality_base
        
    def detect_motion_areas(self, frame_sequence):
        """Detecta áreas com movimento entre frames"""
        if len(frame_sequence) < 2:
            return np.ones(frame_sequence[0].shape[:2], dtype=np.float32)
            
        prev_frame = cv2.cvtColor(frame_sequence[0], cv2.COLOR_BGR2GRAY)
        curr_frame = cv2.cvtColor(frame_sequence[1], cv2.COLOR_BGR2GRAY)
        
        # Calcula fluxo óptico
        flow = cv2.calcOpticalFlowFarneback(
            prev_frame, curr_frame, None, 0.5, 3, 15, 3, 5, 1.2, 0
        )
        
        # Magnitude do movimento
        magnitude, _ = cv2.cartToPolar(flow[..., 0], flow[..., 1])
        motion_mask = cv2.normalize(magnitude, None, 0, 1, cv2.NORM_MINMAX)
        
        return motion_mask
        
    def generate_quality_matrix(self, motion_map):
        """Gera matriz de qualidade baseada no mapa de movimento"""
        # Suaviza o mapa de movimento
        blurred = cv2.GaussianBlur(motion_map, (15, 15), 0)
        
        # Normaliza para o intervalo de qualidade
        quality_range = 30  # Diferença entre maior e menor qualidade
        quality_matrix = self.quality_base - (blurred * quality_range)
        
        return quality_matrix
        
    def compress(self, frame, quality_matrix):
        """Comprime o frame usando compressão perceptiva"""
        # Converte matriz de qualidade para inteiros
        quality_matrix_int = quality_matrix.astype(np.uint8)
        
        # Divide o frame em blocos
        h, w = frame.shape[:2]
        block_size = 16
        compressed = frame.copy()
        
        for y in range(0, h, block_size):
            for x in range(0, w, block_size):
                # Extrai bloco
                block = frame[y:min(y+block_size, h), x:min(x+block_size, w)]
                
                # Determina qualidade para este bloco
                quality = int(np.mean(quality_matrix_int[y:min(y+block_size, h), x:min(x+block_size, w)]))
                
                # Comprime o bloco
                _, encoded = cv2.imencode('.jpg', block, [cv2.IMWRITE_JPEG_QUALITY, quality])
                decoded = cv2.imdecode(encoded, cv2.IMREAD_COLOR)
                
                # Insere de volta
                compressed[y:min(y+block_size, h), x:min(x+block_size, w)] = decoded
                
        return compressed 