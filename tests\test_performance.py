import time
import cv2
import numpy as np
from camera_optimizer import CameraOptimizer

def measure_fps(duration=10):
    """Mede FPS médio durante 'duration' segundos"""
    config = {
        'resolution': (640, 480),
        'fps': 30,
        'redundancy_factor': 1.5,
        'use_gpu': True,
        'buffer_size': 3,
        'compression_quality': 85
    }
    
    frames_processed = 0
    start_time = time.time()
    
    with CameraOptimizer(device_id=0, config=config) as optimizer:
        while (time.time() - start_time) < duration:
            ret, frame = optimizer.cap.read()
            if not ret:
                break
                
            # Processa frame e mede tempo
            t1 = time.time()
            optimized = optimizer.optimize_frame(frame)
            processing_time = time.time() - t1
            
            frames_processed += 1
            
            # Mostra informações em tempo real
            print(f"\rFPS: {frames_processed/(time.time()-start_time):.2f} | "
                  f"Latência: {processing_time*1000:.2f}ms", end="")
            
            # Visualiza o frame
            cv2.imshow('Performance Test', optimized)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    cv2.destroyAllWindows()
    
    # Calcula métricas finais
    total_time = time.time() - start_time
    avg_fps = frames_processed / total_time
    
    print(f"\n\nResultados do Teste de Performance:")
    print(f"Tempo total: {total_time:.2f}s")
    print(f"Frames processados: {frames_processed}")
    print(f"FPS médio: {avg_fps:.2f}")
    
def test_latency():
    """Testa latência com diferentes configurações"""
    configs = [
        {'redundancy_factor': 1.2, 'compression_quality': 85},
        {'redundancy_factor': 1.5, 'compression_quality': 85},
        {'redundancy_factor': 1.8, 'compression_quality': 85}
    ]
    
    print("\nTeste de Latência com Diferentes Configurações:")
    for config in configs:
        optimizer = CameraOptimizer(device_id=0, config=config)
        
        latencies = []
        for _ in range(100):  # Testa com 100 frames
            ret, frame = optimizer.cap.read()
            if not ret:
                break
                
            t1 = time.time()
            optimizer.optimize_frame(frame)
            latencies.append((time.time() - t1) * 1000)  # ms
        
        print(f"\nConfiguração: {config}")
        print(f"Latência média: {np.mean(latencies):.2f}ms")
        print(f"Latência mínima: {np.min(latencies):.2f}ms")
        print(f"Latência máxima: {np.max(latencies):.2f}ms")
        
        optimizer.cap.release()

if __name__ == '__main__':
    print("Iniciando testes de performance...")
    print("\n1. Teste de FPS")
    measure_fps()
    
    print("\n2. Teste de Latência")
    test_latency() 