import cv2
import numpy as np
from camera_optimizer import CameraOptimizer

def main():
    # Configuração personalizada
    config = {
        'resolution': (1280, 720),  # Upscaling da resolução original
        'fps': 30,
        'redundancy_factor': 1.5,
        'use_gpu': True,
        'buffer_size': 3,
        'compression_quality': 85
    }

    # Inicializa o otimizador
    with CameraOptimizer(device_id=0, config=config) as optimizer:
        # Janela para visualização
        cv2.namedWindow('Optimized Camera Feed', cv2.WINDOW_NORMAL)

        # Loop principal
        for frame in optimizer.start_capture():
            # Mostra o frame otimizado
            cv2.imshow('Optimized Camera Feed', frame)

            # Ajusta parâmetros em tempo real com teclas
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('u'):  # Aumenta redundância
                optimizer.adjust_parameters(redundancy_factor=config['redundancy_factor'] + 0.1)
            elif key == ord('d'):  # Di<PERSON>ui redundância
                optimizer.adjust_parameters(redundancy_factor=config['redundancy_factor'] - 0.1)

        cv2.destroyAllWindows()

if __name__ == '__main__':
    main() 