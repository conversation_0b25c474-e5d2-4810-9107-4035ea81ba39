import cv2
import time
import argparse
import numpy as np
from camera_optimizer import StreamingClient

def main():
    # Configuração via linha de comando
    parser = argparse.ArgumentParser(description='Cliente de Streaming para LifeCam VX-2000')
    parser.add_argument('--server', type=str, default='127.0.0.1', help='Endereço IP do servidor')
    parser.add_argument('--port', type=int, default=9999, help='Porta do servidor')
    parser.add_argument('--camera', type=int, default=0, help='ID da câmera')
    parser.add_argument('--quality', type=int, default=90, help='Qualidade de compressão (0-100)')
    parser.add_argument('--fps', type=int, default=30, help='FPS máximo')
    args = parser.parse_args()
    
    print(f"=== Cliente de Streaming para LifeCam VX-2000 ===")
    print(f"Conectando ao servidor: {args.server}:{args.port}")
    print(f"Câmera: {args.camera}, Qualidade: {args.quality}, FPS: {args.fps}")
    print("Pressione 'q' para sair")
    
    # Inicializa cliente
    client = StreamingClient(
        camera_id=args.camera,
        server_ip=args.server,
        server_port=args.port,
        quality=args.quality,
        max_fps=args.fps
    )
    
    # Inicia cliente
    if not client.start():
        print("Falha ao iniciar cliente")
        return
    
    # Cria janela para visualização
    cv2.namedWindow("Streaming Melhorado", cv2.WINDOW_NORMAL)
    
    try:
        # Loop principal
        last_stats_time = time.time()
        
        while True:
            # Obtém frame processado
            frame = client.get_next_frame()
            
            if frame is not None:
                # Verifica se o frame tem o formato correto
                if not isinstance(frame, (np.ndarray)) or len(frame.shape) < 2:
                    print("Frame recebido com formato inválido")
                    continue
                    
                # Obtém estatísticas
                stats = client.get_stats()
                
                # Mostra estatísticas no frame
                info_text = [
                    f"FPS: {stats['receive_fps']:.1f} | Latência: {stats['latency']:.1f}ms",
                    f"Qualidade: {stats['current_quality']} | Compressão: {stats['compression_ratio']:.1f}x",
                    f"Frames Enviados: {stats['frames_sent']} | Recebidos: {stats['frames_received']} | Descartados: {stats.get('frames_skipped', 0)}"
                ]
                
                # Adiciona texto ao frame
                for i, text in enumerate(info_text):
                    y_pos = 30 + (i * 25)
                    cv2.putText(frame, text, (10, y_pos), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                
                # Adiciona indicador visual de latência
                latency = stats['latency']
                if latency < 200:
                    color = (0, 255, 0)  # Verde - boa
                elif latency < 500:
                    color = (0, 255, 255)  # Amarelo - aceitável
                else:
                    color = (0, 0, 255)  # Vermelho - ruim
                
                # Barra de latência com verificação de dimensões
                try:
                    if len(frame.shape) >= 2:
                        frame_width = frame.shape[1]
                        frame_height = frame.shape[0]
                        bar_width = min(int(latency / 20), frame_width - 20)
                        cv2.rectangle(frame, (10, frame_height - 30), 
                                    (10 + bar_width, frame_height - 20), color, -1)
                except Exception as e:
                    print(f"Erro ao desenhar barra de latência: {e}")
                
                # Mostra o frame
                cv2.imshow("Streaming Melhorado", frame)
                
                # Mostra estatísticas detalhadas a cada 5 segundos
                if time.time() - last_stats_time > 5.0:
                    print("\nEstatísticas:")
                    print(f"FPS Envio: {stats['send_fps']:.1f}")
                    print(f"FPS Recebimento: {stats['receive_fps']:.1f}")
                    print(f"Latência: {stats['latency']:.1f}ms")
                    print(f"Qualidade atual: {stats['current_quality']}")
                    print(f"Taxa de Compressão: {stats['compression_ratio']:.1f}x")
                    print(f"Frames Enviados: {stats['frames_sent']}")
                    print(f"Frames Recebidos: {stats['frames_received']}")
                    print(f"Frames Descartados: {stats.get('frames_skipped', 0)}")
                    last_stats_time = time.time()
            
            # Processa teclas
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
    
    finally:
        # Para cliente e libera recursos
        client.stop()
        cv2.destroyAllWindows()
        print("Cliente finalizado")

if __name__ == "__main__":
    main() 