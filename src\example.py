from main import CameraOptimizer

def main():
    optimizer = CameraOptimizer()
    optimizer.initialize()
    
    while True:
        frame = optimizer.video_stream.get_frame()
        if frame is None:
            break
            
        processed_frame = optimizer.frame_processor.process_frame(frame)
        enhanced_frame = optimizer.upscaler.upscale(processed_frame)
        
        # Exibe o resultado
        cv2.imshow('Optimized Camera Feed', enhanced_frame)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

if __name__ == "__main__":
    main() 