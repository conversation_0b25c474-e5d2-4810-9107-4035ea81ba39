import cv2
import numpy as np

class FrameProcessor:
    def __init__(self):
        self.previous_frame = None
        
    def process_frame(self, frame):
        """Pipeline principal de processamento"""
        denoised = self.apply_denoising(frame)
        enhanced = self.enhance_image(denoised)
        return enhanced
        
    def apply_denoising(self, frame):
        return cv2.fastNlMeansDenoisingColored(frame) 