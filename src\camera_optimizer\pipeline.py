from concurrent.futures import Thr<PERSON><PERSON>ool<PERSON>xecutor
import cv2
import numpy as np

class OptimizedPipeline:
    def __init__(self, max_workers=4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
    def denoise_frame(self, frame):
        return cv2.fastNlMeansDenoisingColored(frame)
        
    def enhance_colors(self, frame):
        lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        cl = clahe.apply(l)
        enhanced = cv2.merge((cl,a,b))
        return cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
    def smart_sharpen(self, frame):
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        return cv2.filter2D(frame, -1, kernel)
        
    def process_frame(self, frame):
        future_denoise = self.executor.submit(self.denoise_frame, frame.copy())
        future_enhance = self.executor.submit(self.enhance_colors, frame.copy())
        future_sharpen = self.executor.submit(self.smart_sharpen, frame.copy())
        
        denoised = future_denoise.result()
        enhanced = future_enhance.result()
        sharpened = future_sharpen.result()
        
        # Combina os resultados
        result = cv2.addWeighted(denoised, 0.3, enhanced, 0.4, 0)
        result = cv2.addWeighted(result, 0.7, sharpened, 0.3, 0)
        
        return result 