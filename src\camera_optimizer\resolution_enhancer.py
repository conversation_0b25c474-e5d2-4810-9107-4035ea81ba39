import cv2
import numpy as np
import torch
import torch.nn.functional as F
from PIL import Image
from torchvision import transforms

class ResolutionEnhancer:
    """
    Classe especializada para melhorar a resolução da LifeCam VX-2000,
    convertendo de 640x480 para resoluções maiores como 720p ou 1080p.
    """
    
    RESOLUTIONS = {
        "720p": (1280, 720),
        "1080p": (1920, 1080),
        "480p": (854, 480),
        "original": (640, 480)
    }
    
    def __init__(self, target_resolution="720p", use_gpu=False):
        """
        Inicializa o enhancer de resolução.
        
        Args:
            target_resolution: Resolução alvo ("720p", "1080p", "480p" ou "original")
            use_gpu: Se deve usar GPU para processamento
        """
        self.target_resolution = self._get_resolution(target_resolution)
        self.device = torch.device('cuda' if torch.cuda.is_available() and use_gpu else 'cpu')
        self.transform = transforms.Compose([
            transforms.ToTensor(),
        ])
        
        # Calcula fatores de escala
        self.scale_x = self.target_resolution[0] / 640
        self.scale_y = self.target_resolution[1] / 480
        
        # Inicializa buffer para suavização temporal
        self.previous_frames = []
        self.max_buffer_size = 3
        
    def _get_resolution(self, resolution_name):
        """Obtém dimensões da resolução pelo nome"""
        if isinstance(resolution_name, tuple) and len(resolution_name) == 2:
            return resolution_name
        
        if resolution_name in self.RESOLUTIONS:
            return self.RESOLUTIONS[resolution_name]
        
        # Fallback para 720p se resolução não for reconhecida
        return self.RESOLUTIONS["720p"]
        
    def enhance_resolution(self, frame):
        """
        Melhora a resolução do frame usando técnicas avançadas.
        
        Args:
            frame: Frame de entrada (640x480)
            
        Returns:
            Frame com resolução melhorada
        """
        # Verifica se o frame está na resolução original
        h, w = frame.shape[:2]
        if (w, h) == self.target_resolution:
            return frame
            
        # Pré-processamento para redução de ruído
        denoised = cv2.fastNlMeansDenoisingColored(frame, None, 5, 5, 7, 21)
        
        # Sharpening para melhorar detalhes
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        
        # Converte para tensor PyTorch
        tensor = self.transform(Image.fromarray(sharpened)).unsqueeze(0).to(self.device)
        
        # Upscaling usando interpolação bicúbica
        upscaled = F.interpolate(
            tensor, 
            size=self.target_resolution[::-1],  # altura, largura
            mode='bicubic',
            align_corners=False
        )
        
        # Converte de volta para numpy
        result = upscaled.squeeze(0).permute(1, 2, 0).mul(255).clamp(0, 255).byte().cpu().numpy()
        
        # Aplica suavização temporal se houver frames anteriores
        if len(self.previous_frames) > 0:
            # Média ponderada com frames anteriores para reduzir flickering
            weights = [0.6] + [0.4 / len(self.previous_frames)] * len(self.previous_frames)
            result_float = result.astype(np.float32) * weights[0]
            
            for i, prev_frame in enumerate(self.previous_frames):
                # Redimensiona frame anterior se necessário
                if prev_frame.shape[:2] != result.shape[:2]:
                    prev_frame = cv2.resize(prev_frame, (result.shape[1], result.shape[0]))
                result_float += prev_frame.astype(np.float32) * weights[i+1]
                
            result = result_float.astype(np.uint8)
        
        # Atualiza buffer de frames
        self.previous_frames.append(result.copy())
        if len(self.previous_frames) > self.max_buffer_size:
            self.previous_frames.pop(0)
            
        return result
        
    def enhance_details(self, frame):
        """
        Melhora detalhes específicos no frame de alta resolução.
        
        Args:
            frame: Frame já com resolução aumentada
            
        Returns:
            Frame com detalhes melhorados
        """
        # Converte para LAB para melhorar cores
        lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Aplica CLAHE no canal L
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        cl = clahe.apply(l)
        
        # Reconstrói imagem
        enhanced_lab = cv2.merge((cl,a,b))
        enhanced_bgr = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        
        # Aplica unsharp mask para melhorar bordas
        gaussian = cv2.GaussianBlur(enhanced_bgr, (0, 0), 3)
        enhanced = cv2.addWeighted(enhanced_bgr, 1.5, gaussian, -0.5, 0)
        
        return enhanced
        
    def process(self, frame):
        """
        Processa o frame completo, melhorando resolução e detalhes.
        
        Args:
            frame: Frame original da câmera
            
        Returns:
            Frame processado em alta resolução
        """
        # Aumenta resolução
        high_res = self.enhance_resolution(frame)
        
        # Melhora detalhes
        enhanced = self.enhance_details(high_res)
        
        return enhanced 