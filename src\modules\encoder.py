import numpy as np
from reedsolo import RSCodec

class FrameRedundancyEncoder:
    def __init__(self, redundancy_factor=1.5):
        self.rsc = RSCodec(12)  # Define o nível de correção de erros
        
    def encode_frame(self, frame_data):
        """Aplica codificação Reed-Solomon para redundância"""
        encoded = self.rsc.encode(frame_data.tobytes())
        return np.frombuffer(encoded, dtype=frame_data.dtype) 