import cv2

class VideoStream:
    def __init__(self, device_id=0):
        self.device_id = device_id
        self.capture = cv2.VideoCapture(device_id)
        
    def set_optimal_parameters(self):
        """Configura parâmetros otimizados para a câmera"""
        self.capture.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        self.capture.set(cv2.CAP_PROP_FPS, 30)
        self.capture.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25) 