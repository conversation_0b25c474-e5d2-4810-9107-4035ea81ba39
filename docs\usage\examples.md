# Exemplos de Uso

Este documento apresenta exemplos práticos de uso do Microsoft LifeCam VX-2000 Optimizer.

## Índice

1. [Streaming Básico](#streaming-básico)
2. [Streaming Otimizado para Baixa Latência](#streaming-otimizado-para-baixa-latência)
3. [Personalização de Configurações](#personalização-de-configurações)
4. [Integração em Aplicações](#integração-em-aplicações)
5. [Monitoramento de Desempenho](#monitoramento-de-desempenho)

## Streaming Básico

O modo de streaming básico é ideal para a maioria dos casos de uso, oferecendo um bom equilíbrio entre qualidade e latência.

### Servidor

1. Execute o servidor:
   ```bash
   scripts/run/run_streaming_server.bat
   ```

2. Você verá informações sobre o servidor iniciado:
   ```
   === Iniciando Servidor de Streaming ===
   Servidor iniciado em 0.0.0.0:9999
   Aguardando cone<PERSON>...
   ```

### Cliente

1. Execute o cliente:
   ```bash
   scripts/run/run_streaming_client.bat
   ```

2. Uma janela será aberta mostrando o feed da câmera processado.
3. Estatísticas serão exibidas na janela e no console.
4. Pressione 'q' para sair.

## Streaming Otimizado para Baixa Latência

Para casos onde a latência é crítica, use o modo otimizado para baixa latência.

### Servidor Otimizado

1. Execute o servidor otimizado:
   ```bash
   scripts/run/run_streaming_server_optimized.bat
   ```

2. Este servidor está configurado para:
   - Limitar o número de clientes (2)
   - Usar resolução 720p
   - Priorizar velocidade sobre qualidade

### Cliente de Baixa Latência

1. Execute o cliente de baixa latência:
   ```bash
   scripts/run/run_streaming_client_low_latency.bat
   ```

2. Este cliente está configurado para:
   - Qualidade de compressão reduzida (60)
   - FPS limitado (15)
   - Controle adaptativo agressivo

## Personalização de Configurações

### Modificando Parâmetros do Cliente

Você pode personalizar os parâmetros do cliente através da linha de comando:

```bash
python examples/streaming_client.py --server ************* --quality 80 --fps 25
```

Parâmetros disponíveis:
- `--server`: Endereço IP do servidor
- `--port`: Porta do servidor (padrão: 9999)
- `--camera`: ID da câmera (padrão: 0)
- `--quality`: Qualidade de compressão (0-100, padrão: 90)
- `--fps`: FPS máximo (padrão: 30)

### Modificando Parâmetros do Servidor

Você pode personalizar os parâmetros do servidor através da linha de comando:

```bash
python examples/streaming_server.py --host 0.0.0.0 --port 9999 --max-clients 5 --resolution 720p
```

Parâmetros disponíveis:
- `--host`: Endereço de escuta (padrão: 0.0.0.0)
- `--port`: Porta de escuta (padrão: 9999)
- `--max-clients`: Número máximo de clientes (padrão: 5)
- `--resolution`: Resolução alvo (original, 480p, 720p, 1080p)

## Integração em Aplicações

### Exemplo de Integração em Aplicação Python

```python
import cv2
import numpy as np
import time
from camera_optimizer import StreamingClient

def main():
    # Inicializa cliente
    client = StreamingClient(
        server_ip="127.0.0.1",
        quality=75,
        max_fps=20
    )
    
    # Inicia cliente
    if not client.start():
        print("Falha ao iniciar cliente")
        return
    
    # Cria janela para visualização
    cv2.namedWindow("Minha Aplicação", cv2.WINDOW_NORMAL)
    
    try:
        # Loop principal
        while True:
            # Obtém frame processado
            frame = client.get_next_frame()
            
            if frame is not None:
                # Aplica processamento adicional específico da aplicação
                # Por exemplo, detecção de objetos, reconhecimento facial, etc.
                processed_frame = apply_my_processing(frame)
                
                # Mostra frame
                cv2.imshow("Minha Aplicação", processed_frame)
            
            # Processa teclas
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
    
    finally:
        # Para cliente e libera recursos
        client.stop()
        cv2.destroyAllWindows()
        print("Aplicação finalizada")

def apply_my_processing(frame):
    # Exemplo: converte para escala de cinza e depois de volta para BGR
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    return cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)

if __name__ == "__main__":
    main()
```

### Exemplo de Integração com Interface Gráfica (Tkinter)

```python
import cv2
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import threading
import time
from camera_optimizer import StreamingClient

class CameraApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Camera Optimizer App")
        
        # Configura a interface
        self.setup_ui()
        
        # Inicializa cliente
        self.client = StreamingClient(
            server_ip="127.0.0.1",
            quality=75,
            max_fps=20
        )
        
        # Variáveis de controle
        self.running = False
        self.update_thread = None
        
    def setup_ui(self):
        # Frame principal
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Canvas para exibir o vídeo
        self.canvas = tk.Canvas(main_frame, bg="black")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Frame de controles
        control_frame = ttk.Frame(main_frame, padding=5)
        control_frame.pack(fill=tk.X)
        
        # Botões
        self.start_button = ttk.Button(control_frame, text="Iniciar", command=self.start)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="Parar", command=self.stop, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Estatísticas
        self.stats_label = ttk.Label(control_frame, text="FPS: -- | Latência: -- ms")
        self.stats_label.pack(side=tk.RIGHT, padx=5)
        
    def start(self):
        if not self.running:
            # Inicia cliente
            if self.client.start():
                self.running = True
                self.update_thread = threading.Thread(target=self.update_frame)
                self.update_thread.daemon = True
                self.update_thread.start()
                
                # Atualiza botões
                self.start_button.config(state=tk.DISABLED)
                self.stop_button.config(state=tk.NORMAL)
            else:
                print("Falha ao iniciar cliente")
    
    def stop(self):
        if self.running:
            self.running = False
            if self.update_thread:
                self.update_thread.join(timeout=1.0)
            self.client.stop()
            
            # Atualiza botões
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
    
    def update_frame(self):
        while self.running:
            # Obtém frame processado
            frame = self.client.get_next_frame()
            
            if frame is not None:
                # Converte para formato Tkinter
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                img = Image.fromarray(frame_rgb)
                img_tk = ImageTk.PhotoImage(image=img)
                
                # Atualiza canvas
                self.canvas.config(width=img.width, height=img.height)
                self.canvas.create_image(0, 0, anchor=tk.NW, image=img_tk)
                self.canvas.image = img_tk  # Mantém referência
                
                # Atualiza estatísticas
                stats = self.client.get_stats()
                stats_text = f"FPS: {stats['receive_fps']:.1f} | Latência: {stats['latency']:.1f} ms"
                self.stats_label.config(text=stats_text)
            
            # Pequena pausa para não sobrecarregar a CPU
            time.sleep(0.01)

if __name__ == "__main__":
    root = tk.Tk()
    app = CameraApp(root)
    root.protocol("WM_DELETE_WINDOW", app.stop)  # Garante que o cliente seja parado ao fechar
    root.mainloop()
```

## Monitoramento de Desempenho

### Visualização de Estatísticas em Tempo Real

O cliente de streaming exibe estatísticas em tempo real na interface:

- **FPS**: Taxa de quadros por segundo
- **Latência**: Tempo entre captura e exibição (ms)
- **Qualidade**: Nível atual de compressão
- **Compressão**: Taxa de compressão

Além disso, uma barra colorida na parte inferior da janela indica visualmente a latência:
- **Verde**: Latência baixa (<200ms)
- **Amarelo**: Latência média (200-500ms)
- **Vermelho**: Latência alta (>500ms)

### Registro de Desempenho

Para registrar estatísticas de desempenho em um arquivo:

```python
import csv
import time
from camera_optimizer import StreamingClient

def log_performance(duration_seconds=60, output_file="performance_log.csv"):
    # Inicializa cliente
    client = StreamingClient()
    client.start()
    
    # Prepara arquivo CSV
    with open(output_file, 'w', newline='') as csvfile:
        fieldnames = ['timestamp', 'fps', 'latency', 'quality', 'compression_ratio']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        # Registra estatísticas
        start_time = time.time()
        while time.time() - start_time < duration_seconds:
            # Obtém frame para garantir que o cliente está processando
            client.get_next_frame()
            
            # Obtém estatísticas
            stats = client.get_stats()
            
            # Registra no CSV
            writer.writerow({
                'timestamp': time.time() - start_time,
                'fps': stats['receive_fps'],
                'latency': stats['latency'],
                'quality': stats['current_quality'],
                'compression_ratio': stats['compression_ratio']
            })
            
            # Pequena pausa
            time.sleep(1.0)  # Registra a cada segundo
    
    # Para cliente
    client.stop()
    print(f"Registro de desempenho concluído. Dados salvos em {output_file}")

if __name__ == "__main__":
    log_performance(duration_seconds=60)
```

Este script registrará estatísticas de desempenho por 60 segundos e salvará em um arquivo CSV que pode ser analisado posteriormente em ferramentas como Excel, Python (pandas) ou R. 
