import unittest
import sys
import os
from time import sleep

def run_all_tests():
    # Executa testes unitários
    print("Executando testes unitários...")
    unittest.TextTestRunner(verbosity=2).run(
        unittest.defaultTestLoader.discover('tests')
    )
    
    # Pequena pausa para separar os resultados
    sleep(1)
    
    # Executa testes de performance
    print("\nExecutando testes de performance...")
    os.system('python tests/test_performance.py')

if __name__ == '__main__':
    print("=== Iniciando Suite de Testes do Camera Optimizer ===\n")
    run_all_tests() 