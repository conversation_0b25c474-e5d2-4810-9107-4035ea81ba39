import cv2
import numpy as np
import socket
import pickle
import struct
import threading
import time
import queue
from typing import Optional, Dict, Any, List, Tuple, Callable

class ClientHandler:
    """Gerencia a conexão com um cliente específico"""
    
    def __init__(self, client_socket, client_address, processor_func):
        self.client_socket = client_socket
        self.client_address = client_address
        self.processor_func = processor_func
        
        self.running = False
        self.receive_thread = None
        self.process_thread = None
        self.send_thread = None
        
        # Reduz tamanho das filas para evitar acúmulo
        self.receive_queue = queue.Queue(maxsize=3)
        self.send_queue = queue.Queue(maxsize=3)
        
        self.config = None
        
        # Controle de descarte de frames
        self.skip_frames = False
        self.frame_counter = 0
        self.skip_threshold = 5  # Processa 1 a cada N frames quando sobrecarregado
        
        # Estatísticas
        self.stats = {
            'frames_received': 0,
            'frames_processed': 0,
            'frames_sent': 0,
            'frames_skipped': 0,
            'receive_fps': 0,
            'process_fps': 0,
            'send_fps': 0,
            'processing_time': 0,
            'queue_size': 0,
            'client_address': client_address
        }
    
    def start(self):
        """Inicia o tratamento do cliente"""
        if self.running:
            return
            
        self.running = True
        
        # Recebe configuração inicial
        self._receive_config()
        
        # Inicia threads
        self.receive_thread = threading.Thread(target=self._receive_thread_func)
        self.receive_thread.daemon = True
        self.receive_thread.start()
        
        self.process_thread = threading.Thread(target=self._process_thread_func)
        self.process_thread.daemon = True
        self.process_thread.start()
        
        self.send_thread = threading.Thread(target=self._send_thread_func)
        self.send_thread.daemon = True
        self.send_thread.start()
        
        print(f"Iniciado tratamento para cliente: {self.client_address}")
    
    def stop(self):
        """Para o tratamento do cliente"""
        self.running = False
        
        # Aguarda threads terminarem
        if self.receive_thread:
            self.receive_thread.join(timeout=1.0)
            
        if self.process_thread:
            self.process_thread.join(timeout=1.0)
            
        if self.send_thread:
            self.send_thread.join(timeout=1.0)
            
        # Fecha socket
        if self.client_socket:
            self.client_socket.close()
            
        print(f"Finalizado tratamento para cliente: {self.client_address}")
    
    def _receive_config(self):
        """Recebe configuração inicial do cliente"""
        try:
            # Recebe tamanho da configuração
            data = b""
            payload_size = struct.calcsize("L")
            
            while len(data) < payload_size:
                packet = self.client_socket.recv(4096)
                if not packet:
                    return False
                data += packet
                
            packed_size = data[:payload_size]
            data = data[payload_size:]
            msg_size = struct.unpack("L", packed_size)[0]
            
            # Recebe dados da configuração
            while len(data) < msg_size:
                packet = self.client_socket.recv(4096)
                if not packet:
                    return False
                data += packet
                
            # Extrai configuração
            config_data = data[:msg_size]
            self.config = pickle.loads(config_data)
            
            print(f"Configuração recebida do cliente {self.client_address}: {self.config}")
            return True
            
        except Exception as e:
            print(f"Erro ao receber configuração: {e}")
            return False
    
    def _receive_thread_func(self):
        """Thread para receber frames do cliente"""
        data = b""
        payload_size = struct.calcsize("L")
        receive_count = 0
        start_time = time.time()
        
        while self.running:
            try:
                # Recebe tamanho do pacote
                while len(data) < payload_size:
                    packet = self.client_socket.recv(4096)
                    if not packet:
                        self.running = False
                        break
                    data += packet
                
                if not self.running:
                    break
                    
                # Extrai tamanho do pacote
                packed_size = data[:payload_size]
                data = data[payload_size:]
                msg_size = struct.unpack("L", packed_size)[0]
                
                # Recebe dados do pacote
                while len(data) < msg_size:
                    packet = self.client_socket.recv(4096)
                    if not packet:
                        self.running = False
                        break
                    data += packet
                
                if not self.running:
                    break
                    
                # Extrai pacote
                frame_data = data[:msg_size]
                data = data[msg_size:]
                
                # Desempacota
                compressed_frame, timestamp = pickle.loads(frame_data)
                
                # Coloca na fila de processamento
                if not self.receive_queue.full():
                    self.receive_queue.put((compressed_frame, timestamp))
                    self.stats['frames_received'] += 1
                    
                # Atualiza estatísticas
                receive_count += 1
                elapsed = time.time() - start_time
                if elapsed >= 1.0:
                    self.stats['receive_fps'] = receive_count / elapsed
                    receive_count = 0
                    start_time = time.time()
                    
            except Exception as e:
                print(f"Erro ao receber frame do cliente {self.client_address}: {e}")
                self.running = False
                break
    
    def _process_thread_func(self):
        """Thread para processar frames"""
        process_count = 0
        start_time = time.time()
        
        while self.running:
            try:
                # Verifica tamanho da fila para controle de sobrecarga
                queue_size = self.receive_queue.qsize()
                self.stats['queue_size'] = queue_size
                
                # Ativa modo de descarte se a fila estiver muito grande
                if queue_size > 2:
                    self.skip_frames = True
                elif queue_size <= 1:
                    self.skip_frames = False
                
                # Obtém frame da fila
                compressed_frame, timestamp = self.receive_queue.get(timeout=1.0)
                
                # Descarta frames se necessário para reduzir latência
                if self.skip_frames:
                    self.frame_counter += 1
                    if self.frame_counter % self.skip_threshold != 0:
                        self.stats['frames_skipped'] += 1
                        continue
                
                # Decodifica frame com tratamento de erro
                try:
                    frame = cv2.imdecode(compressed_frame, cv2.IMREAD_COLOR)
                    
                    # Verifica se o frame é válido
                    if frame is None or frame.size == 0:
                        print(f"Frame inválido recebido de {self.client_address}")
                        continue
                        
                    # Processa frame com tratamento de erro
                    try:
                        t1 = time.time()
                        processed_frame = self.processor_func(frame, self.config)
                        processing_time = time.time() - t1
                        
                        # Verifica se o frame processado é válido
                        if processed_frame is None or processed_frame.size == 0:
                            print(f"Erro: processamento gerou frame inválido para {self.client_address}")
                            # Usa o frame original como fallback
                            processed_frame = frame
                            
                        # Atualiza estatísticas
                        self.stats['processing_time'] = processing_time * 1000  # ms
                        
                        # Comprime frame processado com qualidade adaptativa
                        # Reduz qualidade se estiver com latência alta
                        quality = self.config.get('quality', 90)
                        if self.skip_frames:
                            quality = max(50, quality - 20)  # Reduz qualidade para melhorar performance
                        
                        _, compressed_processed = cv2.imencode('.jpg', processed_frame, 
                                                             [cv2.IMWRITE_JPEG_QUALITY, quality])
                        
                        # Coloca na fila de envio
                        if not self.send_queue.full():
                            self.send_queue.put((compressed_processed, timestamp))
                            self.stats['frames_processed'] += 1
                    except Exception as e:
                        print(f"Erro ao processar frame: {e}")
                        # Tenta enviar o frame original como fallback
                        _, compressed_original = cv2.imencode('.jpg', frame, 
                                                            [cv2.IMWRITE_JPEG_QUALITY, 70])
                        if not self.send_queue.full():
                            self.send_queue.put((compressed_original, timestamp))
                except Exception as e:
                    print(f"Erro ao decodificar frame: {e}")
                    continue
                
                # Atualiza estatísticas
                process_count += 1
                elapsed = time.time() - start_time
                if elapsed >= 1.0:
                    self.stats['process_fps'] = process_count / elapsed
                    process_count = 0
                    start_time = time.time()
                    
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Erro ao processar frame: {e}")
                time.sleep(0.1)
    
    def _send_thread_func(self):
        """Thread para enviar frames processados de volta para o cliente"""
        send_count = 0
        start_time = time.time()
        
        while self.running:
            try:
                # Obtém frame da fila
                compressed_frame, timestamp = self.send_queue.get(timeout=1.0)
                
                # Prepara pacote para envio
                data = pickle.dumps((compressed_frame, timestamp))
                
                # Envia tamanho do pacote primeiro
                size = struct.pack("L", len(data))
                self.client_socket.sendall(size + data)
                
                # Atualiza estatísticas
                self.stats['frames_sent'] += 1
                send_count += 1
                elapsed = time.time() - start_time
                if elapsed >= 1.0:
                    self.stats['send_fps'] = send_count / elapsed
                    send_count = 0
                    start_time = time.time()
                    
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Erro ao enviar frame para cliente {self.client_address}: {e}")
                self.running = False
                break
    
    def is_running(self):
        """Verifica se o tratamento do cliente está em execução"""
        return self.running
    
    def get_stats(self):
        """Obtém estatísticas do cliente"""
        return self.stats


class StreamingServer:
    """
    Servidor de streaming que recebe frames dos clientes,
    processa e retorna os frames processados.
    """
    
    def __init__(
        self,
        host: str = "0.0.0.0",
        port: int = 9999,
        max_clients: int = 5,
        processor_func: Optional[Callable] = None
    ):
        """
        Inicializa o servidor de streaming.
        
        Args:
            host: Endereço IP para bind do servidor
            port: Porta para bind do servidor
            max_clients: Número máximo de clientes simultâneos
            processor_func: Função para processar frames
        """
        self.host = host
        self.port = port
        self.max_clients = max_clients
        self.processor_func = processor_func
        
        self.server_socket = None
        self.running = False
        self.accept_thread = None
        
        self.clients = []
        self.clients_lock = threading.Lock()
    
    def _default_processor(self, frame, config):
        """Processador padrão de frames (apenas redimensiona)"""
        target_resolution = config.get('target_resolution', (1280, 720))
        # Usa método mais rápido para redimensionamento
        return cv2.resize(frame, target_resolution, interpolation=cv2.INTER_LINEAR)
    
    def _accept_connections(self):
        """Thread para aceitar conexões de clientes"""
        self.server_socket.listen(self.max_clients)
        print(f"Servidor de streaming iniciado em {self.host}:{self.port}")
        
        while self.running:
            try:
                # Aceita conexão
                client_socket, client_address = self.server_socket.accept()
                print(f"Nova conexão de: {client_address}")
                
                # Cria handler para o cliente
                processor = self.processor_func or self._default_processor
                client_handler = ClientHandler(client_socket, client_address, processor)
                
                # Adiciona à lista de clientes
                with self.clients_lock:
                    self.clients.append(client_handler)
                    
                # Inicia tratamento do cliente
                client_handler.start()
                
            except Exception as e:
                print(f"Erro ao aceitar conexão: {e}")
                time.sleep(0.1)
    
    def _cleanup_clients(self):
        """Remove clientes inativos"""
        with self.clients_lock:
            active_clients = []
            for client in self.clients:
                if client.is_running():
                    active_clients.append(client)
                else:
                    client.stop()
            self.clients = active_clients
    
    def start(self):
        """Inicia o servidor de streaming"""
        if self.running:
            print("Servidor já está em execução")
            return False
            
        try:
            # Cria socket do servidor
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            
            # Inicia thread de aceitação
            self.running = True
            self.accept_thread = threading.Thread(target=self._accept_connections)
            self.accept_thread.daemon = True
            self.accept_thread.start()
            
            # Inicia thread de limpeza
            self.cleanup_thread = threading.Thread(target=self._cleanup_thread_func)
            self.cleanup_thread.daemon = True
            self.cleanup_thread.start()
            
            return True
            
        except Exception as e:
            print(f"Erro ao iniciar servidor: {e}")
            return False
    
    def _cleanup_thread_func(self):
        """Thread para limpar clientes inativos periodicamente"""
        while self.running:
            self._cleanup_clients()
            time.sleep(5.0)
    
    def stop(self):
        """Para o servidor de streaming"""
        self.running = False
        
        # Para todos os clientes
        with self.clients_lock:
            for client in self.clients:
                client.stop()
            self.clients = []
            
        # Fecha socket do servidor
        if self.server_socket:
            self.server_socket.close()
            
        # Aguarda thread de aceitação terminar
        if self.accept_thread:
            self.accept_thread.join(timeout=1.0)
            
        print("Servidor de streaming parado")
    
    def get_stats(self) -> List[Dict[str, Any]]:
        """
        Obtém estatísticas de todos os clientes.
        
        Returns:
            Lista de dicionários com estatísticas por cliente
        """
        with self.clients_lock:
            return [client.get_stats() for client in self.clients]
    
    def get_client_count(self) -> int:
        """
        Obtém o número de clientes conectados.
        
        Returns:
            Número de clientes
        """
        with self.clients_lock:
            return len(self.clients)
    
    def __enter__(self):
        self.start()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop() 