import unittest
import numpy as np
import cv2
from camera_optimizer import CameraOptimizer, FrameRedundancyEncoder

class TestCameraOptimizer(unittest.TestCase):
    def setUp(self):
        self.test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        self.config = {
            'resolution': (640, 480),
            'fps': 30,
            'redundancy_factor': 1.5,
            'use_gpu': False,  # Usa CPU para testes
            'buffer_size': 3,
            'compression_quality': 85
        }
        
    def test_encoder_initialization(self):
        encoder = FrameRedundancyEncoder()
        self.assertIsNotNone(encoder)
        
    def test_frame_encoding_decoding(self):
        encoder = FrameRedundancyEncoder()
        encoded = encoder.encode_frame(self.test_frame)
        decoded = encoder.decode_frame(encoded, self.test_frame.shape)
        self.assertEqual(decoded.shape, self.test_frame.shape)
        
    def test_optimizer_initialization(self):
        optimizer = CameraOptimizer(device_id=0, config=self.config)
        self.assertEqual(optimizer.config['resolution'], (640, 480))
        optimizer.cap.release()
        
    def test_parameter_adjustment(self):
        optimizer = CameraOptimizer(device_id=0, config=self.config)
        new_quality = 90
        optimizer.adjust_parameters(compression_quality=new_quality)
        self.assertEqual(optimizer.config['compression_quality'], new_quality)
        optimizer.cap.release()

if __name__ == '__main__':
    unittest.main() 