# Guia de Instalação

Este guia fornece instruções detalhadas para instalar e configurar o Microsoft LifeCam VX-2000 Optimizer.

## Requisitos de Sistema

### Hardware
- Processador: Intel Core i3 ou equivalente (recomendado i5 ou superior)
- Memória RAM: 4GB mínimo (recomendado 8GB)
- Microsoft LifeCam VX-2000 conectada via USB 2.0
- Conexão de rede para streaming (recomendado conexão cabeada)

### Software
- Windows 10 ou 11 (64 bits)
- Python 3.7 ou superior
- Drivers da câmera Microsoft LifeCam VX-2000 instalados

## Instalação Passo a Passo

### 1. Preparação do Ambiente

1. Verifique se o Python está instalado:
   ```bash
   python --version
   ```
   Se não estiver instalado, baixe e instale a versão mais recente do [site oficial do Python](https://www.python.org/downloads/).

2. Recomendamos criar um ambiente virtual:
   ```bash
   # Instale o virtualenv se necessário
   pip install virtualenv
   
   # Crie um ambiente virtual
   virtualenv venv
   
   # Ative o ambiente virtual
   # No Windows:
   venv\Scripts\activate
   # No Linux/Mac:
   source venv/bin/activate
   ```

### 2. Instalação do Projeto

1. Clone o repositório:
   ```bash
   git clone https://github.com/seu-usuario/microsoft-cam.git
   cd microsoft-cam
   ```

2. Instale as dependências:
   ```bash
   # Instalação básica
   pip install -r requirements/base.txt
   
   # Para desenvolvimento (opcional)
   pip install -r requirements/dev.txt
   
   # Para testes (opcional)
   pip install -r requirements/test.txt
   ```

3. Instale o pacote em modo de desenvolvimento:
   ```bash
   pip install -e .
   ```

### 3. Verificação da Instalação

1. Teste a câmera:
   ```bash
   python scripts/test/test_camera.py
   ```
   Deve abrir uma janela mostrando o feed da câmera.

2. Verifique se todas as dependências foram instaladas corretamente:
   ```bash
   pip list
   ```
   Confirme que todas as bibliotecas listadas em `requirements/base.txt` estão presentes.

## Solução de Problemas

### Câmera não detectada

1. Verifique se a câmera está conectada corretamente:
   ```bash
   python -c "import cv2; print(cv2.VideoCapture(0).isOpened())"
   ```
   Deve retornar `True` se a câmera estiver funcionando.

2. Verifique os drivers:
   - Abra o Gerenciador de Dispositivos do Windows
   - Procure por "Câmeras" ou "Dispositivos de Imagem"
   - Verifique se a Microsoft LifeCam VX-2000 está listada sem erros
   - Se necessário, reinstale os drivers do [site da Microsoft](https://www.microsoft.com/accessories/pt-br/downloads)

### Erros de Dependências

1. Se encontrar erros relacionados a bibliotecas:
   ```bash
   pip install --upgrade --force-reinstall -r requirements/base.txt
   ```

2. Para problemas com OpenCV:
   ```bash
   pip uninstall opencv-python
   pip install opencv-python-headless
   ```

3. Para problemas com PyTorch:
   Visite [pytorch.org](https://pytorch.org/get-started/locally/) e siga as instruções específicas para seu sistema.

## Configuração Avançada

### Otimização para GPU

Se você possui uma GPU NVIDIA compatível com CUDA:

1. Instale o CUDA Toolkit e cuDNN apropriados para sua GPU
2. Instale a versão do PyTorch com suporte a CUDA:
   ```bash
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
   ```
   (Substitua `cu118` pela versão do CUDA que você instalou)

3. Verifique se o PyTorch reconhece sua GPU:
   ```bash
   python -c "import torch; print(torch.cuda.is_available())"
   ```
   Deve retornar `True` se tudo estiver configurado corretamente.

### Configuração de Rede para Streaming

Para melhor desempenho em streaming:

1. Configure seu firewall para permitir tráfego nas portas usadas (padrão: 9999)
2. Se estiver usando em rede local, considere usar endereços IP estáticos
3. Para streaming pela internet, configure o encaminhamento de porta no seu roteador

## Próximos Passos

Após a instalação bem-sucedida, consulte:

- [Exemplos de Uso](../usage/examples.md) para começar a usar o sistema
- [API de Streaming](../api/streaming.md) para detalhes sobre a API

---

Se você encontrar problemas não cobertos neste guia, por favor [abra uma issue](https://github.com/seu-usuario/microsoft-cam/issues) no repositório. 
