import cv2
import time
import argparse
import threading
from camera_optimizer import StreamingServer, VideoProcessor

def display_stats(server):
    """Thread para mostrar estatísticas do servidor"""
    while True:
        stats = server.get_stats()
        client_count = server.get_client_count()
        
        print(f"\n=== Estatísticas do Servidor ({client_count} clientes) ===")
        
        for client_stats in stats:
            addr = client_stats['client_address']
            print(f"Cliente {addr}:")
            print(f"  Frames Recebidos: {client_stats['frames_received']}")
            print(f"  Frames Processados: {client_stats['frames_processed']}")
            print(f"  Frames Enviados: {client_stats['frames_sent']}")
            print(f"  FPS Recebimento: {client_stats['receive_fps']:.1f}")
            print(f"  FPS Processamento: {client_stats['process_fps']:.1f}")
            print(f"  FPS Envio: {client_stats['send_fps']:.1f}")
            print(f"  Tempo de Processamento: {client_stats['processing_time']:.1f}ms")
            print("")
            
        time.sleep(5.0)

def main():
    # Configuração via linha de comando
    parser = argparse.ArgumentParser(description='Servidor de Streaming para LifeCam VX-2000')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='Endereço IP para bind')
    parser.add_argument('--port', type=int, default=9999, help='Porta para bind')
    parser.add_argument('--max-clients', type=int, default=5, help='Número máximo de clientes')
    parser.add_argument('--gpu', action='store_true', help='Usar GPU para processamento')
    parser.add_argument('--resolution', type=str, default='720p', 
                       choices=['720p', '1080p', '480p', 'original'], 
                       help='Resolução alvo')
    args = parser.parse_args()
    
    print(f"=== Servidor de Streaming para LifeCam VX-2000 ===")
    print(f"Iniciando servidor em {args.host}:{args.port}")
    print(f"Máximo de clientes: {args.max_clients}")
    print(f"Usando GPU: {args.gpu}")
    print(f"Resolução alvo: {args.resolution}")
    
    # Inicializa processador de vídeo
    processor = VideoProcessor(use_gpu=args.gpu)
    
    # Função de processamento personalizada
    def process_frame(frame, config):
        # Adiciona resolução alvo se não estiver definida
        if 'target_resolution' not in config:
            config['target_resolution'] = args.resolution
            
        # Adiciona ID do cliente para suavização temporal
        client_id = str(config.get('client_address', 'unknown'))
        config['client_id'] = client_id
        
        # Processa frame
        return processor(frame, config)
    
    # Inicializa servidor
    server = StreamingServer(
        host=args.host,
        port=args.port,
        max_clients=args.max_clients,
        processor_func=process_frame
    )
    
    # Inicia servidor
    if not server.start():
        print("Falha ao iniciar servidor")
        return
        
    try:
        # Inicia thread para mostrar estatísticas
        stats_thread = threading.Thread(target=display_stats, args=(server,))
        stats_thread.daemon = True
        stats_thread.start()
        
        print("Servidor iniciado. Pressione Ctrl+C para sair.")
        
        # Mantém o servidor rodando
        while True:
            time.sleep(1.0)
            
    except KeyboardInterrupt:
        print("\nEncerrando servidor...")
    finally:
        # Para servidor
        server.stop()
        print("Servidor finalizado")

if __name__ == "__main__":
    main() 