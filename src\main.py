import cv2
import numpy as np
from concurrent.futures import ThreadPoolExecutor
from modules.video_capture import VideoStream
from modules.frame_processor import FrameProcessor
from modules.encoder import FrameRedundancyEncoder
from modules.upscaler import SmartUpscaler
from modules.cache import PredictiveCache

class CameraOptimizer:
    def __init__(self):
        self.video_stream = VideoStream(device_id=0)
        self.frame_processor = FrameProcessor()
        self.encoder = FrameRedundancyEncoder()
        self.upscaler = SmartUpscaler()
        self.cache = PredictiveCache()
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    def initialize(self):
        """Inicializa as configurações da câmera"""
        self.video_stream.set_optimal_parameters()
        return self.video_stream.get_device_info() 