# API de Streaming

Esta documentação descreve a API de streaming do Microsoft LifeCam VX-2000 Optimizer.

## Visão Geral

A API de streaming é composta por três componentes principais:

1. **StreamingClient**: Captura frames da câmera e envia para o servidor
2. **StreamingServer**: <PERSON>cebe frames dos clientes, processa e retorna
3. **VideoProcessor**: Aplica melhorias de qualidade nos frames

```
Cliente → Servidor → Processador → Servidor → Cliente
```

## Classes Principais

### StreamingClient

```python
from camera_optimizer import StreamingClient

client = StreamingClient(
    camera_id=0,               # ID da câmera (padrão: 0)
    server_ip="127.0.0.1",     # Endereço IP do servidor
    server_port=9999,          # Porta do servidor
    quality=75,                # Qualidade de compressão JPEG (0-100)
    max_fps=20                 # FPS máximo
)
```

#### Métodos

| Método | Descri<PERSON> |
|--------|-----------|
| `start()` | Inicia o cliente de streaming |
| `stop()` | Para o cliente de streaming |
| `get_next_frame()` | Obtém o próximo frame processado |
| `get_stats()` | Obtém estatísticas de desempenho |

#### Exemplo de Uso

```python
import cv2
from camera_optimizer import StreamingClient

# Inicializa cliente
client = StreamingClient(server_ip="*************")

# Inicia cliente
client.start()

try:
    while True:
        # Obtém frame processado
        frame = client.get_next_frame()
        
        if frame is not None:
            # Mostra frame
            cv2.imshow("Frame Processado", frame)
            
            # Obtém estatísticas
            stats = client.get_stats()
            print(f"FPS: {stats['receive_fps']:.1f}, Latência: {stats['latency']:.1f}ms")
        
        # Sai com ESC
        if cv2.waitKey(1) == 27:
            break
finally:
    client.stop()
    cv2.destroyAllWindows()
```

### StreamingServer

```python
from camera_optimizer import StreamingServer, VideoProcessor

# Cria processador de vídeo
processor = VideoProcessor()

# Inicializa servidor
server = StreamingServer(
    host="0.0.0.0",           # Endereço de escuta (0.0.0.0 = todas interfaces)
    port=9999,                # Porta de escuta
    max_clients=5,            # Número máximo de clientes
    processor_func=processor  # Função de processamento
)
```

#### Métodos

| Método | Descrição |
|--------|-----------|
| `start()` | Inicia o servidor de streaming |
| `stop()` | Para o servidor de streaming |
| `get_stats()` | Obtém estatísticas de todos os clientes |
| `get_client_count()` | Obtém o número de clientes conectados |

#### Exemplo de Uso

```python
import time
from camera_optimizer import StreamingServer, VideoProcessor

# Cria processador com configurações personalizadas
processor = VideoProcessor(use_gpu=True)

# Inicializa servidor
server = StreamingServer(processor_func=processor)

# Inicia servidor
server.start()

try:
    print("Servidor iniciado. Pressione Ctrl+C para sair.")
    
    # Loop para mostrar estatísticas
    while True:
        stats = server.get_stats()
        clients = server.get_client_count()
        
        print(f"Clientes conectados: {clients}")
        for i, client_stats in enumerate(stats):
            print(f"Cliente {i+1}: FPS={client_stats['process_fps']:.1f}, "
                  f"Frames processados={client_stats['frames_processed']}")
        
        time.sleep(5)  # Atualiza a cada 5 segundos
except KeyboardInterrupt:
    print("Encerrando servidor...")
finally:
    server.stop()
```

### VideoProcessor

```python
from camera_optimizer import VideoProcessor

processor = VideoProcessor(use_gpu=False)  # use_gpu=True para usar GPU se disponível
```

#### Métodos

| Método | Descrição |
|--------|-----------|
| `process_frame(frame, config, client_id)` | Processa um frame com configurações específicas |
| `__call__(frame, config)` | Alias para process_frame (sem client_id) |

#### Configurações

O dicionário `config` pode conter as seguintes opções:

```python
config = {
    "resolution": "720p",      # Resolução alvo: "original", "480p", "720p", "1080p"
    "denoise": 5,              # Força do denoising (0-10)
    "enhance_details": True,   # Ativar melhoria de detalhes
    "temporal_smoothing": True, # Ativar suavização temporal
    "quality": 90              # Qualidade de compressão JPEG (0-100)
}
```

#### Exemplo de Uso

```python
import cv2
import numpy as np
from camera_optimizer import VideoProcessor

# Inicializa processador
processor = VideoProcessor()

# Configuração
config = {
    "resolution": "720p",
    "denoise": 3,
    "enhance_details": True
}

# Captura frame da câmera
cap = cv2.VideoCapture(0)
ret, frame = cap.read()
cap.release()

if ret:
    # Processa frame
    processed = processor.process_frame(frame, config)
    
    # Mostra resultado
    cv2.imshow("Original", frame)
    cv2.imshow("Processado", processed)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
```

## Controle Adaptativo

O sistema implementa controle adaptativo para otimizar a qualidade e latência:

1. **Qualidade Adaptativa**: Reduz a qualidade de compressão quando a latência aumenta
2. **Descarte de Frames**: Descarta frames quando a fila de processamento está cheia
3. **Processamento Adaptativo**: Simplifica o processamento em condições de alta carga

### Parâmetros de Controle

| Parâmetro | Descrição | Valor Padrão |
|-----------|-----------|--------------|
| `latency_threshold` | Limiar de latência para adaptação (ms) | 500 |
| `min_quality` | Qualidade mínima de compressão | 50 |
| `skip_threshold` | Taxa de descarte de frames | 3 (1 a cada 3) |

## Métricas e Estatísticas

O sistema fornece métricas em tempo real para monitoramento:

### Métricas do Cliente

```python
stats = client.get_stats()
```

| Métrica | Descrição |
|---------|-----------|
| `frames_sent` | Número total de frames enviados |
| `frames_received` | Número total de frames recebidos |
| `frames_skipped` | Número total de frames descartados |
| `send_fps` | Taxa de envio de frames por segundo |
| `receive_fps` | Taxa de recebimento de frames por segundo |
| `latency` | Latência média (ms) |
| `compression_ratio` | Taxa de compressão média |
| `current_quality` | Qualidade atual de compressão |

### Métricas do Servidor

```python
stats = server.get_stats()
```

| Métrica | Descrição |
|---------|-----------|
| `frames_received` | Número total de frames recebidos |
| `frames_processed` | Número total de frames processados |
| `frames_sent` | Número total de frames enviados |
| `frames_skipped` | Número total de frames descartados |
| `process_fps` | Taxa de processamento de frames por segundo |
| `processing_time` | Tempo médio de processamento (ms) |
| `queue_size` | Tamanho atual da fila de processamento |

## Tratamento de Erros

O sistema implementa tratamento robusto de erros:

1. **Verificação de Frames**: Valida frames antes do processamento
2. **Recuperação de Falhas**: Usa frames originais como fallback em caso de erro
3. **Reconexão Automática**: Tenta reconectar em caso de perda de conexão

## Limitações Conhecidas

- Latência mínima de aproximadamente 100-200ms em redes locais
- Resolução máxima limitada pela câmera (640x480 para LifeCam VX-2000)
- Upscaling para 720p/1080p pode introduzir artefatos visuais
- Desempenho do processador afeta significativamente a latência
