import cv2
import numpy as np
import torch
import torch.nn.functional as F
from PIL import Image
from torchvision import transforms
from typing import Dict, Any, Tuple, Optional
import time

class VideoProcessor:
    """
    Processador de vídeo que aplica melhorias de qualidade em frames.
    """
    
    RESOLUTIONS = {
        "720p": (1280, 720),
        "1080p": (1920, 1080),
        "480p": (854, 480),
        "original": (640, 480)
    }
    
    def __init__(self, use_gpu: bool = False):
        """
        Inicializa o processador de vídeo.
        
        Args:
            use_gpu: Se deve usar GPU para processamento
        """
        self.use_gpu = use_gpu
        self.device = torch.device('cuda' if torch.cuda.is_available() and use_gpu else 'cpu')
        self.transform = transforms.Compose([
            transforms.ToTensor(),
        ])
        
        # Inicializa buffer para suavização temporal
        self.previous_frames = {}  # client_id -> [frames]
        self.max_buffer_size = 2  # Reduzido para diminuir uso de memória
        
        # Controle de performance
        self.processing_times = []
        self.adaptive_mode = True  # Modo adaptativo para ajustar qualidade baseado na performance
        
        print(f"VideoProcessor inicializado (GPU: {self.use_gpu}, Device: {self.device})")
    
    def _get_resolution(self, resolution_name):
        """Obtém dimensões da resolução pelo nome"""
        if isinstance(resolution_name, tuple) and len(resolution_name) == 2:
            return resolution_name
        
        if resolution_name in self.RESOLUTIONS:
            return self.RESOLUTIONS[resolution_name]
        
        # Fallback para 720p se resolução não for reconhecida
        return self.RESOLUTIONS["720p"]
    
    def _denoise(self, frame, strength=5):
        """
        Aplica redução de ruído no frame com intensidade adaptativa
        Versão otimizada para performance
        """
        # Versão mais rápida de denoising para baixa latência
        if len(self.processing_times) > 10 and np.mean(self.processing_times[-10:]) > 50:
            # Se o tempo de processamento for alto, usa um método mais simples
            return cv2.GaussianBlur(frame, (5, 5), 0)
        else:
            # Usa parâmetros mais leves para o fastNlMeans
            return cv2.fastNlMeansDenoisingColored(frame, None, strength, strength, 7, 11)
    
    def _enhance_details(self, frame):
        """
        Melhora detalhes no frame
        Versão otimizada para performance
        """
        # Sharpening para melhorar detalhes (versão simplificada)
        kernel = np.array([[-0.5,-0.5,-0.5], [-0.5,5,-0.5], [-0.5,-0.5,-0.5]])
        sharpened = cv2.filter2D(frame, -1, kernel)
        
        # Melhora contraste (versão simplificada)
        if len(self.processing_times) > 10 and np.mean(self.processing_times[-10:]) > 50:
            # Se o tempo de processamento for alto, pula o CLAHE
            return sharpened
        
        lab = cv2.cvtColor(sharpened, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        cl = clahe.apply(l)
        enhanced_lab = cv2.merge((cl,a,b))
        
        return cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
    
    def _upscale(self, frame, target_resolution):
        """
        Aumenta a resolução do frame
        Versão otimizada para performance
        """
        # Verifica se o upscaling é necessário
        h, w = frame.shape[:2]
        if (w, h) == target_resolution:
            return frame
            
        # Método adaptativo baseado na performance
        if self.adaptive_mode and len(self.processing_times) > 10:
            avg_time = np.mean(self.processing_times[-10:])
            
            # Se o tempo médio de processamento for alto, usa método mais rápido
            if avg_time > 50:  # ms
                return cv2.resize(frame, target_resolution, interpolation=cv2.INTER_LINEAR)
        
        # Usa PyTorch para upscaling de alta qualidade
        try:
            # Converte para tensor PyTorch
            tensor = self.transform(Image.fromarray(frame)).unsqueeze(0).to(self.device)
            
            # Upscaling usando interpolação bicúbica
            upscaled = F.interpolate(
                tensor, 
                size=target_resolution[::-1],  # altura, largura
                mode='bicubic',
                align_corners=False
            )
            
            # Converte de volta para numpy
            result = upscaled.squeeze(0).permute(1, 2, 0).mul(255).clamp(0, 255).byte().cpu().numpy()
            
            return result
        except Exception as e:
            print(f"Erro no upscaling PyTorch: {e}, usando OpenCV")
            return cv2.resize(frame, target_resolution, interpolation=cv2.INTER_CUBIC)
    
    def _apply_temporal_smoothing(self, frame, client_id):
        """
        Aplica suavização temporal para reduzir flickering
        Versão otimizada para performance
        """
        # Inicializa buffer para este cliente se não existir
        if client_id not in self.previous_frames:
            self.previous_frames[client_id] = []
            
        # Se não houver frames anteriores, retorna o frame atual
        if len(self.previous_frames[client_id]) == 0:
            self.previous_frames[client_id].append(frame.copy())
            return frame
            
        # Versão simplificada para melhor performance
        # Usa apenas o último frame para suavização
        prev_frame = self.previous_frames[client_id][-1]
        
        # Redimensiona frame anterior se necessário
        if prev_frame.shape[:2] != frame.shape[:2]:
            prev_frame = cv2.resize(prev_frame, (frame.shape[1], frame.shape[0]))
            
        # Média ponderada simples
        result = cv2.addWeighted(frame, 0.7, prev_frame, 0.3, 0)
        
        # Atualiza buffer
        self.previous_frames[client_id].append(frame.copy())
        if len(self.previous_frames[client_id]) > self.max_buffer_size:
            self.previous_frames[client_id].pop(0)
            
        return result
    
    def process_frame(self, frame: np.ndarray, config: Dict[str, Any], client_id: Optional[str] = None) -> np.ndarray:
        """
        Processa um frame aplicando melhorias de qualidade.
        
        Args:
            frame: Frame a ser processado
            config: Configurações de processamento
            client_id: ID do cliente (para suavização temporal)
            
        Returns:
            Frame processado
        """
        start_time = time.time()
        
        # Extrai configurações
        target_resolution_name = config.get('target_resolution', '720p')
        target_resolution = self._get_resolution(target_resolution_name)
        
        # Verifica se o frame está vazio
        if frame is None or frame.size == 0:
            return np.zeros((*target_resolution[::-1], 3), dtype=np.uint8)
        
        # Redimensiona primeiro para reduzir carga de processamento
        h, w = frame.shape[:2]
        if (w, h) != target_resolution:
            # Redimensiona para resolução alvo com método rápido
            frame = cv2.resize(frame, target_resolution, interpolation=cv2.INTER_LINEAR)
        
        # Aplica redução de ruído (com força adaptativa)
        denoising_strength = 5
        if self.adaptive_mode and len(self.processing_times) > 10:
            avg_time = np.mean(self.processing_times[-10:])
            if avg_time > 50:  # ms
                denoising_strength = 3  # Reduz força para melhorar performance
                
        denoised = self._denoise(frame, denoising_strength)
        
        # Melhora detalhes
        enhanced = self._enhance_details(denoised)
        
        # Aplica suavização temporal se client_id for fornecido
        if client_id is not None:
            result = self._apply_temporal_smoothing(enhanced, client_id)
        else:
            result = enhanced
            
        # Registra tempo de processamento
        processing_time = (time.time() - start_time) * 1000  # ms
        self.processing_times.append(processing_time)
        if len(self.processing_times) > 100:
            self.processing_times.pop(0)
            
        return result
    
    def __call__(self, frame: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
        """
        Permite usar o processador como uma função.
        
        Args:
            frame: Frame a ser processado
            config: Configurações de processamento
            
        Returns:
            Frame processado
        """
        # Extrai client_id do config se existir
        client_id = config.get('client_id', None)
        
        # Adiciona target_resolution se não existir
        if 'target_resolution' not in config:
            config['target_resolution'] = '720p'
            
        return self.process_frame(frame, config, client_id) 