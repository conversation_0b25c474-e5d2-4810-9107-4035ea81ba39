import cv2

def test_camera():
    print("Iniciando teste de câmera...")
    
    # Tenta abrir a câmera
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("ERRO: Não foi possível abrir a câmera!")
        return
    
    print("Câmera aberta com sucesso!")
    print(f"Resolução: {int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x{int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}")
    
    # Captura alguns frames
    print("Capturando frames...")
    for i in range(10):
        ret, frame = cap.read()
        if not ret:
            print(f"ERRO: Falha ao capturar frame {i}!")
            break
        print(f"Frame {i} capturado: {frame.shape}")
    
    # Mostra a imagem
    print("Mostrando janela de visualização...")
    cv2.namedWindow("Teste de Camera", cv2.WINDOW_NORMAL)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("ERRO: Falha ao capturar frame!")
            break
            
        cv2.imshow("Teste de Camera", frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
    
    # Libera recursos
    cap.release()
    cv2.destroyAllWindows()
    print("Teste finalizado!")

if __name__ == "__main__":
    test_camera() 