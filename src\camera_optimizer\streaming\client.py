import cv2
import numpy as np
import socket
import pickle
import struct
import threading
import time
import queue
from typing import Optional, Tuple, Dict, Any

class StreamingClient:
    """
    Cliente de streaming que captura frames da câmera e envia para o servidor.
    Recebe frames processados de volta e os exibe.
    """
    
    def __init__(
        self, 
        camera_id: int = 0,
        server_ip: str = "127.0.0.1",
        server_port: int = 9999,
        quality: int = 90,
        max_fps: int = 30,
        buffer_size: int = 5
    ):
        """
        Inicializa o cliente de streaming.
        
        Args:
            camera_id: ID da câmera a ser usada
            server_ip: Endereço IP do servidor
            server_port: Porta do servidor
            quality: Qualidade de compressão JPEG (0-100)
            max_fps: Taxa máxima de quadros por segundo
            buffer_size: Tamanho do buffer de frames
        """
        self.camera_id = camera_id
        self.server_ip = server_ip
        self.server_port = server_port
        self.quality = quality
        self.max_fps = max_fps
        self.min_frame_time = 1.0 / max_fps
        
        # Inicializa a câmera
        self.cap = None
        
        # Buffers para frames (reduzidos para menor latência)
        self.send_queue = queue.Queue(maxsize=2)
        self.receive_queue = queue.Queue(maxsize=2)
        
        # Controle de threads
        self.running = False
        self.capture_thread = None
        self.send_thread = None
        self.receive_thread = None
        
        # Socket para comunicação
        self.client_socket = None
        
        # Controle adaptativo
        self.adaptive_quality = True
        self.current_quality = quality
        self.min_quality = 50
        self.latency_threshold = 500  # ms
        self.skip_frames = False
        self.frame_counter = 0
        self.skip_threshold = 3  # Envia 1 a cada N frames quando latência alta
        
        # Estatísticas
        self.stats = {
            'frames_sent': 0,
            'frames_received': 0,
            'frames_skipped': 0,
            'send_fps': 0,
            'receive_fps': 0,
            'latency': 0,
            'compression_ratio': 0,
            'last_frame_time': 0,
            'current_quality': quality
        }
        
    def _setup_camera(self):
        """Configura a câmera para captura"""
        self.cap = cv2.VideoCapture(self.camera_id)
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        self.cap.set(cv2.CAP_PROP_FPS, self.max_fps)
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimiza latência
        
        if not self.cap.isOpened():
            raise RuntimeError(f"Não foi possível abrir a câmera {self.camera_id}")
            
        print(f"Câmera inicializada: {int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x"
              f"{int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))} @ "
              f"{int(self.cap.get(cv2.CAP_PROP_FPS))}fps")
    
    def _connect_to_server(self):
        """Estabelece conexão com o servidor"""
        try:
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.connect((self.server_ip, self.server_port))
            print(f"Conectado ao servidor: {self.server_ip}:{self.server_port}")
            
            # Envia configurações para o servidor
            config = {
                'quality': self.quality,
                'max_fps': self.max_fps,
                'resolution': (640, 480)
            }
            
            config_data = pickle.dumps(config)
            config_size = struct.pack("L", len(config_data))
            self.client_socket.sendall(config_size + config_data)
            
            return True
        except Exception as e:
            print(f"Erro ao conectar ao servidor: {e}")
            return False
    
    def _capture_thread_func(self):
        """Thread para capturar frames da câmera"""
        last_frame_time = time.time()
        
        while self.running:
            # Controle de FPS
            current_time = time.time()
            elapsed = current_time - last_frame_time
            
            if elapsed < self.min_frame_time:
                time.sleep(self.min_frame_time - elapsed)
                
            # Captura frame
            ret, frame = self.cap.read()
            
            if not ret:
                print("Erro ao capturar frame da câmera")
                time.sleep(0.1)
                continue
                
            # Controle de descarte de frames para reduzir latência
            if self.skip_frames:
                self.frame_counter += 1
                if self.frame_counter % self.skip_threshold != 0:
                    self.stats['frames_skipped'] += 1
                    continue
                
            # Adiciona timestamp para medir latência
            timestamp = time.time()
            
            # Ajusta qualidade baseado na latência
            if self.adaptive_quality and self.stats['latency'] > self.latency_threshold:
                # Reduz qualidade se latência for alta
                self.current_quality = max(self.min_quality, 
                                          self.current_quality - 5)
                # Ativa descarte de frames se latência for muito alta
                if self.stats['latency'] > self.latency_threshold * 2:
                    self.skip_frames = True
            elif self.adaptive_quality and self.stats['latency'] < self.latency_threshold / 2:
                # Aumenta qualidade se latência for baixa
                self.current_quality = min(self.quality, 
                                          self.current_quality + 1)
                self.skip_frames = False
                
            self.stats['current_quality'] = self.current_quality
            
            # Comprime o frame para JPEG para reduzir tamanho
            _, compressed = cv2.imencode('.jpg', frame, 
                                        [cv2.IMWRITE_JPEG_QUALITY, 
                                         int(self.current_quality)])
            
            # Calcula taxa de compressão
            original_size = frame.nbytes
            compressed_size = compressed.nbytes
            compression_ratio = original_size / compressed_size
            
            # Atualiza estatísticas
            self.stats['compression_ratio'] = compression_ratio
            
            # Coloca na fila de envio
            try:
                if not self.send_queue.full():
                    self.send_queue.put((compressed, timestamp), block=False)
                    last_frame_time = time.time()
                    self.stats['frames_sent'] += 1
                else:
                    # Se a fila estiver cheia, ativa descarte de frames
                    self.skip_frames = True
            except queue.Full:
                # Se a fila estiver cheia, ativa descarte de frames
                self.skip_frames = True
    
    def _send_thread_func(self):
        """Thread para enviar frames para o servidor"""
        send_count = 0
        start_time = time.time()
        
        while self.running:
            try:
                # Obtém frame da fila
                compressed, timestamp = self.send_queue.get(timeout=1.0)
                
                # Prepara pacote para envio
                data = pickle.dumps((compressed, timestamp))
                
                # Envia tamanho do pacote primeiro
                size = struct.pack("L", len(data))
                self.client_socket.sendall(size + data)
                
                # Atualiza estatísticas
                send_count += 1
                elapsed = time.time() - start_time
                if elapsed >= 1.0:
                    self.stats['send_fps'] = send_count / elapsed
                    send_count = 0
                    start_time = time.time()
                    
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Erro ao enviar frame: {e}")
                time.sleep(0.1)
    
    def _receive_thread_func(self):
        """Thread para receber frames processados do servidor"""
        data = b""
        payload_size = struct.calcsize("L")
        receive_count = 0
        start_time = time.time()
        
        while self.running:
            try:
                # Recebe tamanho do pacote
                while len(data) < payload_size:
                    packet = self.client_socket.recv(4096)
                    if not packet:
                        break
                    data += packet
                
                if not data:
                    print("Conexão fechada pelo servidor")
                    break
                    
                # Extrai tamanho do pacote
                packed_size = data[:payload_size]
                data = data[payload_size:]
                msg_size = struct.unpack("L", packed_size)[0]
                
                # Recebe dados do pacote
                while len(data) < msg_size:
                    packet = self.client_socket.recv(4096)
                    if not packet:
                        break
                    data += packet
                
                # Extrai pacote
                frame_data = data[:msg_size]
                data = data[msg_size:]
                
                # Desempacota com tratamento de erro
                try:
                    compressed_frame, original_timestamp = pickle.loads(frame_data)
                    
                    # Calcula latência
                    latency = time.time() - original_timestamp
                    self.stats['latency'] = latency * 1000  # ms
                    
                    # Decodifica o frame comprimido
                    try:
                        frame = cv2.imdecode(compressed_frame, cv2.IMREAD_COLOR)
                        
                        # Verifica se o frame é válido
                        if frame is None or frame.size == 0:
                            print("Frame inválido recebido")
                            continue
                            
                        # Coloca na fila de recebimento
                        if not self.receive_queue.full():
                            self.receive_queue.put(frame)
                            self.stats['frames_received'] += 1
                    except Exception as e:
                        print(f"Erro ao decodificar frame: {e}")
                        continue
                    
                    # Atualiza estatísticas
                    receive_count += 1
                    elapsed = time.time() - start_time
                    if elapsed >= 1.0:
                        self.stats['receive_fps'] = receive_count / elapsed
                        receive_count = 0
                        start_time = time.time()
                except Exception as e:
                    print(f"Erro ao desempacotar dados: {e}")
                    continue
                
            except Exception as e:
                print(f"Erro ao receber frame: {e}")
                time.sleep(0.1)
    
    def start(self):
        """Inicia o cliente de streaming"""
        if self.running:
            print("Cliente já está em execução")
            return False
            
        # Configura câmera
        self._setup_camera()
        
        # Conecta ao servidor
        if not self._connect_to_server():
            return False
            
        # Inicia threads
        self.running = True
        
        self.capture_thread = threading.Thread(target=self._capture_thread_func)
        self.capture_thread.daemon = True
        self.capture_thread.start()
        
        self.send_thread = threading.Thread(target=self._send_thread_func)
        self.send_thread.daemon = True
        self.send_thread.start()
        
        self.receive_thread = threading.Thread(target=self._receive_thread_func)
        self.receive_thread.daemon = True
        self.receive_thread.start()
        
        print("Cliente de streaming iniciado")
        return True
    
    def stop(self):
        """Para o cliente de streaming"""
        self.running = False
        
        # Aguarda threads terminarem
        if self.capture_thread:
            self.capture_thread.join(timeout=1.0)
            
        if self.send_thread:
            self.send_thread.join(timeout=1.0)
            
        if self.receive_thread:
            self.receive_thread.join(timeout=1.0)
            
        # Fecha socket
        if self.client_socket:
            self.client_socket.close()
            
        # Libera câmera
        if self.cap:
            self.cap.release()
            
        print("Cliente de streaming parado")
    
    def get_next_frame(self) -> Optional[np.ndarray]:
        """
        Obtém o próximo frame processado da fila.
        
        Returns:
            Frame processado ou None se não houver frame disponível
        """
        try:
            return self.receive_queue.get(block=False)
        except queue.Empty:
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Obtém estatísticas do streaming.
        
        Returns:
            Dicionário com estatísticas
        """
        return self.stats
    
    def __enter__(self):
        self.start()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop() 